package com.ecco.webApi.controllers;

import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.config.dom.QListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.finance.FinanceReceipt;
import com.ecco.dom.finance.QFinanceReceipt;
import com.ecco.dom.repairs.QRepair;
import com.ecco.dom.repairs.Repair;
import com.ecco.finance.webApi.dto.ClientSalesChargeInvoiceDetailResource;
import com.ecco.repositories.repairs.RepairRateRepository;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.serviceConfig.dom.QQuestion;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.webApi.calendar.EventResourceAssembler;
import com.ecco.config.service.SettingsService;
import com.ecco.dao.*;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dao.querydsl.EntityRestrictionCommonPredicates;
import com.ecco.dao.querydsl.PredicateSupport;
import com.ecco.dom.*;
import com.ecco.dom.commands.DeleteServiceRecipientCommand;
import com.ecco.dom.groupsupport.GroupActivity_Referral;
import com.ecco.dom.groupsupport.QGroupActivity_Referral;
import com.ecco.dom.groupsupport.QGroupSupportActivity;
import com.ecco.dom.servicerecipients.QBaseServiceRecipient;
import com.ecco.dom.servicerecipients.QBaseServiceRecipientCommand;
import com.ecco.dom.servicerecipients.QServiceRecipientCommand;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.dom.servicerecipients.commands.QServiceRecipientCommandArchive;
import com.ecco.dom.servicerecipients.commands.ServiceRecipientCommandArchive;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.evidence.dom.TaskStatus;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.spring.data.QueryModifier;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.security.dom.QUser;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.dom.QServiceCategorisation;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.webApi.buildings.FixedContainerViewModelAdapter;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.contacts.*;
import com.ecco.webApi.contacts.address.AddressHistoryToViewModel;
import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import com.ecco.webApi.contacts.occupancy.OccupancyViewModel;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.finance.FinanceReceiptToViewModel;
import com.ecco.webApi.finance.FinanceReceiptViewModel;
import com.ecco.webApi.finance.FinanceService;
import com.ecco.webApi.groupSupport.ClientAttendanceToViewModel;
import com.ecco.webApi.groupSupport.ClientAttendanceViewModel;
import com.ecco.webApi.groupSupport.GroupSupportActivitySummaryRowResource;
import com.ecco.webApi.repairs.RepairToViewModel;
import com.ecco.webApi.repairs.RepairViewModel;
import com.ecco.webApi.upload.UploadedFileResourceAssembler;
import com.ecco.webApi.users.UserResourceAssembler;
import com.ecco.webApi.viewModels.*;
import com.ecco.service.BuildingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Range;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.ecco.security.config.AclConfig;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.*;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.ecco.config.service.SettingsService.Reports.*;
import static com.ecco.dom.QEvidenceSupportWork.evidenceSupportWork;
import static com.ecco.dom.QEvidenceThreatWork.evidenceThreatWork;
import static com.ecco.dom.QReferral.referral;
import static com.ecco.dom.QReview.review;
import static com.ecco.evidence.dom.QTaskStatus.taskStatus;
import static com.querydsl.core.types.ExpressionUtils.and;
import static com.querydsl.core.types.ExpressionUtils.or;
import static com.querydsl.core.types.PathMetadataFactory.forVariable;
import static java.util.stream.Collectors.toList;

@PreAuthorize("hasAnyRole('ROLE_REPORTS', 'ROLE_STAFF')")
@RestController
@ReadOnlyTransaction
public class ReportController extends BaseWebApiController {

    private final ClientToViewModel clientToViewModel;
    private final ReferralToViewModel referralToViewModel;
    private final ReferralSummaryToViewModel referralSummaryToViewModel;
    private final AgencyToViewModel agencyToViewModel = new AgencyToViewModel();
    private final IndividualToViewModel individualToViewModel = new IndividualToViewModel();
    private final ServiceRecipientAssociatedContactToViewModel associatedContactToViewModel;

    private final EvidenceSupportWorkToViewModel evidenceSupportWorkToEvidenceViewModel = new EvidenceSupportWorkToViewModel();
    private final EvidenceThreatWorkToViewModel evidenceThreatWorkToEvidenceViewModel = new EvidenceThreatWorkToViewModel(false);
    private final QuestionnaireWorkSummaryToViewModel questionnaireWorkSummaryToViewModel;
    private final EvidenceFormSnapshotToViewModel evidenceFormSnapshotToViewModel;
    private final UserResourceAssembler userResourceAssembler;
    private final TaskStatusToViewModel taskStatusToViewModel;
    private final ReviewToViewModel reviewToViewModel = new ReviewToViewModel();
    private final EvidenceSupportFlagsToViewModel evidenceSupportFlagsToViewModel = new EvidenceSupportFlagsToViewModel();
    private final EvidenceThreatFlagsToViewModel evidenceThreatFlagsToViewModel = new EvidenceThreatFlagsToViewModel();
    private final EvidenceRiskAreaToViewModel genericTypeRiskAreaToViewModel = new EvidenceRiskAreaToViewModel();
    private final FixedContainerViewModelAdapter buildingToViewModel;
    private final RepairToViewModel repairToViewModel;

    private final EntityRestrictionService entityRestrictionService;
    private final RepositoryBasedServiceCategorisationService serviceCategorisationService;
    private final FinanceService financeService;
    private final EvidenceSupportWorkRepository supportWorkRepository;
    private final EvidenceSupportAnswerRepository supportAnswerRepository;
    private final EvidenceSupportCommentRepository supportCommentRepository;
    private final QuestionnaireWorkRepository questionnaireWorkRepository;
    private final EvidenceFormSnapshotRepository evidenceFormSnapshotRepository;
    private final EvidenceQuestionAnswerRepository questionAnswerRepository;
    private final ClientRepository clientRepository;
    private final AgencyRepository agencyRepository;
    private final IndividualRepository individualRepository;
    private final ContactRepository contactRepository;
    private final ServiceRecipientCommandRepository serviceRecipientCommandRepository;
    private final ReferralRepository referralRepository;
    private final GroupSupportActivityInvolvementRepository involvementRepository;
    private final TaskStatusRepository taskStatusRepository;
    private final SettingsService settingsService;
    private final UserRepository userRepository;
    private final FixedContainerRepository buildingRepository;
    private final BuildingService buildingService;
    private final TaskDefinitionService taskDefinitionService;
    private final EventResourceAssembler eventResourceAssembler;
    private final ReportUnsecuredDelegator reportUnsecuredDelegator;
    private final ObjectMapper objectMapper;

    public static final QAbstractBaseWorkEvidence qGTSW = new QAbstractBaseWorkEvidence(EvidenceSupportWork.class, forVariable("evidenceSupportWork"),
            new PathInits("*", "comment.*", "serviceRecipient.*", "serviceRecipient.serviceAllocation.*"));
    public static final QAbstractBaseWorkEvidence qGTTW = new QAbstractBaseWorkEvidence(EvidenceThreatWork.class, forVariable("evidenceThreatWork"),
            new PathInits("*", "comment.*", "serviceRecipient.*", "serviceRecipient.serviceAllocation.*"));
    public static final QAbstractBaseWorkEvidence qEFS = new QAbstractBaseWorkEvidence(EvidenceFormWork.class, forVariable("evidenceFormWork"),
            new PathInits("*", "comment.*", "serviceRecipient.*", "serviceRecipient.serviceAllocation.*"));

    @NonNull
    private ServiceRecipientExtractCommandViewModelJson extractJsonBody;
    @NonNull
    private ExtractCommandViewModelJson baseExtractJsonBody;

    public ReportController(EntityRestrictionService entityRestrictionService,
                            FinanceService financeService,
                            ClientRepository clientRepository,
                            ReferralRepository referralRepository,
                            EvidenceSupportWorkRepository supportWorkRepository,
                            EvidenceSupportCommentRepository supportCommentRepository,
                            EvidenceSupportAnswerRepository supportAnswerRepository,
                            QuestionnaireWorkRepository questionnaireWorkRepository,
                            EvidenceQuestionAnswerRepository questionAnswerRepository,
                            GroupSupportActivityInvolvementRepository involvementRepository,
                            AgencyRepository agencyRepository,
                            IndividualRepository individualRepository,
                            ContactRepository contactRepository,
                            SettingsService settingsService,
                            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                            TaskStatusRepository taskStatusRepository,
                            ServiceRecipientRepository serviceRecipientRepository,
                            UserRepository userRepository,
                            EvidenceFormSnapshotRepository evidenceFormSnapshotRepository,
                            FixedContainerRepository buildingRepository,
                            BuildingService buildingService,
                            ServiceTypeService serviceTypeService,
                            ServiceCategorisationRepository serviceCategorisationRepository,
                            RepositoryBasedServiceCategorisationService serviceCategorisationService,
                            TaskDefinitionService taskDefinitionService,
                            ClientToViewModel clientToViewModel,
                            @NonNull ServiceRecipientExtractCommandViewModelJson extractJsonBody,
                            @NonNull ExtractCommandViewModelJson baseExtractJsonBody,
                            EventResourceAssembler eventResourceAssembler,
                            ReportUnsecuredDelegator reportUnsecuredDelegator,
                            ListDefinitionRepository listDefinitionRepository,
                            FixedContainerViewModelAdapter fixedContainerViewModelAdapter,
                            RepairRateRepository repairRateRepository,
                            ObjectMapper objectMapper,
                            AclConfig aclConfig,
                            ApplicationProperties appProps) {
        this.entityRestrictionService = entityRestrictionService;
        this.financeService = financeService;
        this.clientRepository = clientRepository;
        this.referralRepository = referralRepository;
        this.supportCommentRepository = supportCommentRepository;
        this.supportWorkRepository = supportWorkRepository;
        this.supportAnswerRepository = supportAnswerRepository;
        this.questionnaireWorkRepository = questionnaireWorkRepository;
        this.questionAnswerRepository = questionAnswerRepository;
        this.involvementRepository = involvementRepository;
        this.agencyRepository = agencyRepository;
        this.individualRepository = individualRepository;
        this.contactRepository = contactRepository;
        this.settingsService = settingsService;
        this.serviceRecipientCommandRepository = serviceRecipientCommandRepository;
        this.userRepository = userRepository;
        this.evidenceFormSnapshotRepository = evidenceFormSnapshotRepository;
        this.taskStatusRepository = taskStatusRepository;
        this.buildingRepository = buildingRepository;
        this.buildingService = buildingService;
        this.taskDefinitionService = taskDefinitionService;
        this.eventResourceAssembler = eventResourceAssembler;
        this.extractJsonBody = extractJsonBody;
        this.taskStatusToViewModel = new TaskStatusToViewModel(serviceRecipientRepository);
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
        this.clientToViewModel = clientToViewModel;
        this.objectMapper = objectMapper;
        this.baseExtractJsonBody = baseExtractJsonBody;
        this.serviceCategorisationService = serviceCategorisationService;
        this.repairToViewModel = new RepairToViewModel(listDefinitionRepository, repairRateRepository);
        this.referralToViewModel = new ReferralToViewModel(listDefinitionRepository);
        this.referralSummaryToViewModel = new ReferralSummaryToViewModel();
        this.associatedContactToViewModel = new ServiceRecipientAssociatedContactToViewModel(this.contactRepository, this.serviceRecipientCommandRepository);
        UploadedFileResourceAssembler assembler = new UploadedFileResourceAssembler(appProps.getApplicationRootPath());
        this.questionnaireWorkSummaryToViewModel = new QuestionnaireWorkSummaryToViewModel(assembler);
        this.buildingToViewModel = fixedContainerViewModelAdapter;

        this.userResourceAssembler = new UserResourceAssembler(aclConfig);
        evidenceFormSnapshotToViewModel = new EvidenceFormSnapshotToViewModel(taskDefinitionService);
    }

    @PersistenceContext
    protected EntityManager em;

    // UserViewModel - holds user details with an IndividualViewModel (see UserController.findOneByUsername, data-import and create)
    //      this loads the whole User object (passwordhistory and groups -> authorities)
    //          but we'd need a new repo anyway to get them paged (the current impl is hand-crafted sql)
    //          so could use projection or just rely on paging
    //          and the User is the domain we need in reporting first
    //      it would not adhere to acl restrictions
    //          an acl report could then load the users 'fetchRelatedEntities'
    //          but there is work to do on this already to apply the filter to the acls (although could probably just do service level)
    //          and we'd need to to the User report anyway, so avoid this for now
    // AclEntryViewModel - holds username with permissions (see AclController '/acls/entries/' for listing all acls from admin page)
    //      could be used to restrict by service/project that has permissions for
    //      but ideally acl's would need to be migrated to ServiceCategoriation for a ObjectIdentityId (see AclServiceImpl:110)
    //          (migrate through liquibase, load acls for each user, save new acls with new class, then use the new acls)
    //      or we just use the service level (service/project combination can and should be avoided)
    // IndividualUserSummary - holds username with real name (see UserManagementServiceImpl.getAllUserIndividuals, just a wrapper)
    // It makes sense to use the UserViewModel and use the IndividualViewModel - adding 'fetchRelatedEntities' for AclEntryViewModel if required
    // SO either an EntityGraph or just a specific spring-data OR we could use the cached AclEntryViewModel approach
    @PostJsonReturningJson("/reports/users/page/{page}/")
    @PreAuthorize("hasAnyRole('ROLE_ADMINLOGIN')")
    public Iterable<UserViewModel> reportUsers(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeUsers).asPageRequest(page);

        // the query generated is:
        // select u.* from users u where exists (select 1 from group_members gm inner join groups g inner join group_authorities ga where u.id=gm.member_id and ga.authority like '%ROLE_STAFF%' escape '!') limit ?
        // followed by a select for each user for their contact, passwordhistories, groupmemberships (which are eager)
        // NB groupmemberships loading is bad though, there is a select from group_members entry per user to hydrate the info
        // so we could do a projection, but we do expect the groups to be loaded, and we are paged anyway, so leave for now
        QUser user = QUser.user;
        JPQLQuery<User> query = reportUnsecuredDelegator.query(user)
                .where(user.groupMemberships.any().group.authorities.any().authority.contains("ROLE_STAFF"));
        query.limit(pr.getPageSize()).offset(pr.getOffset());

        List<User> users = query.select(user).fetch();
        return StreamSupport.stream(users.spliterator(), false)
                .map(userResourceAssembler::apply).collect(toList());
    }

    /**
     * Return the buildings according to the report criteria.
     */
    @PostJsonReturningJson("/reports/buildings/page/{page}/")
    public Iterable<FixedContainerViewModel> reportBuildings(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        //Predicate pSecurity = EntityRestrictionCommonPredicates.applySecurityPredicate(
        //        QFixedContainer.fixedContainer.serviceRecipient._super._super, dto, entityRestrictionService);
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeClients).asPageRequest(page);
        var bldgs = buildingService.findBuildingViewModelsWithPagination(pr);

        return bldgs.stream()
                .map(buildingToViewModel::toModel).collect(toList());
    }

    /**
     * Return the repairs according to the report criteria.
     */
    @PostJsonReturningJson("/reports/repairs/page/{page}/")
    public Iterable<RepairViewModel> reportRepairs(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        QRepair qRepair = QRepair.repair;
        QBaseServiceRecipient qSr = QBaseServiceRecipient.baseServiceRecipient;

        var dtoFrom = JodaToJDKAdapters.localDateToJDk(dto.getFromDate());
        var dtoTo = dto.getToDate() != null ? JodaToJDKAdapters.localDateToJDk(dto.getToDate()) : LocalDate.now();
        BooleanExpression pDates = qRepair.receivedDate.gt(dtoFrom).and(qRepair.receivedDate.loe(dtoTo));

        Predicate pServiceRecipient = EntityRestrictionCommonPredicates.applySecurityPredicate(qSr, dto, entityRestrictionService, serviceCategorisationService);
        BooleanExpression sameSrId = qRepair.serviceRecipientId.eq(qSr.id);

        if (dto.getServiceRecipientFilter() != null) {
            sameSrId = qSr.id.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":"))).and(sameSrId);
        }

        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager
        JPQLQuery<Repair> query = reportUnsecuredDelegator.query(qRepair)
                .innerJoin(qSr).on(qRepair.serviceRecipientId.eq(qSr.id))
                .where(sameSrId, pServiceRecipient, pDates)
                .orderBy(qRepair.receivedDate.desc());

        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeDefault).asPageRequest(page);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        var results = query.select(qRepair).fetch();

        return results.stream().map(this.repairToViewModel).collect(toList());
    }

    /**
     * Return the finance service charges according to the report criteria.
     */
    @PostJsonReturningJson("/reports/financeCharges/page/{page}/")
    public Iterable<ClientSalesChargeInvoiceDetailResource.Line> reportFinanceCharges(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        // get a page of address history, to then get the charges
        var adrHistory = reportUnsecuredDelegator.addressHistory(page, dto, true);
        // get the voids... if we are a bld

        // openClosed is > lower, <= upper
        Range<Instant> dates = Range.openClosed(JodaToJDKAdapters.localDateToJDk(dto.getFromDate()).atStartOfDay().toInstant(ZoneOffset.UTC),
                                                dto.getToDate() != null ? JodaToJDKAdapters.localDateToJDk(dto.getToDate()).atStartOfDay().toInstant(ZoneOffset.UTC) : Instant.now());
        final Iterable<ClientSalesChargeInvoiceDetailResource.Line> charges = financeService.getCharges(adrHistory, dates);

        return StreamSupport.stream(charges.spliterator(), false)
                .collect(toList());
    }

    /**
     * Return the finance receipts according to the report criteria.
     */
    @PostJsonReturningJson("/reports/financeReceipts/page/{page}/")
    public Iterable<FinanceReceiptViewModel> reportFinanceReceipts(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        QFinanceReceipt qReceipt = QFinanceReceipt.financeReceipt;
        QBaseServiceRecipient qSr = QBaseServiceRecipient.baseServiceRecipient;

        // dates of address changes
        var dtoFrom = JodaToJDKAdapters.localDateToJDk(dto.getFromDate());
        var dtoTo = dto.getToDate() != null ? JodaToJDKAdapters.localDateToJDk(dto.getToDate()) : LocalDate.now();
        BooleanExpression pDates = qReceipt.receivedDate.goe(dtoFrom).and(qReceipt.receivedDate.lt(dtoTo));

        Predicate pServiceRecipient = EntityRestrictionCommonPredicates.applySecurityPredicate(qSr, dto, entityRestrictionService, serviceCategorisationService);
        BooleanExpression sameSrId = qReceipt.serviceRecipientId.eq(qSr.id);

        if (dto.getServiceRecipientFilter() != null) {
            sameSrId = qSr.id.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":"))).and(sameSrId);
        }

        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager
        JPQLQuery<FinanceReceipt> query = reportUnsecuredDelegator.query(qReceipt)
                .innerJoin(qSr).on(qReceipt.serviceRecipientId.eq(qSr.id))
                .where(sameSrId, pServiceRecipient, pDates)
                .orderBy(qReceipt.receivedDate.desc());

        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeDefault).asPageRequest(page);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        var results = query.select(qReceipt).fetch();

        var convert = new FinanceReceiptToViewModel();
        return results.stream().map(convert).collect(toList());
    }

    /**
     * Return the clients according to the report criteria.
     * The criteria date range refers to fields on the referral depending on the report criteria status.
     */
    @PostJsonReturningJson("/reports/clients/page/{page}/")
    public Iterable<ClientViewModel> reportClients(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        Predicate p = ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeClients).asPageRequest(page);
        final Iterable<ClientDetailWrapper> clients = referralRepository.findAllClientsFromReferralsPredicate(p, pr);

        return StreamSupport.stream(clients.spliterator(), false)
                .map(ClientDetailWrapper::getClient)
                .map(clientToViewModel::apply).collect(toList());
    }

    /**
     * Return the referrals according to the report criteria.
     * The criteria date range refers to fields on the referral depending on the report criteria status.
     */
    @PostJsonReturningJson("/reports/referrals/page/{page}/")
    public Iterable<ReferralViewModel> reportReferrals(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return reportUnsecuredDelegator.reportReferrals(page, dto);
    }

    /**
     * This is used for the initial demo of ad-hoc reporting and acceptance tests
     */
    @PostJsonReturningJson("/reports/referrals/")
    public Iterable<ReferralViewModel> reportReferrals(@RequestBody ReportCriteriaDto dto) {
        return reportUnsecuredDelegator.reportReferrals(null, dto);
    }

    /**
     * Return the referrals according to the report criteria.
     * The criteria date range refers to fields on the referral depending on the report criteria status.
     */
    @PostJsonReturningJson("/reports/referrals/summary/page/{page}/")
    public Stream<ReferralSummaryViewModel> reportReferralSummary(@PathVariable Integer page, @RequestBody ReportCriteriaDto dto) {
        PageRequest pr = page != null ? reportUnsecuredDelegator.getPageSize(PageSizeReferrals).asPageRequest(page) : null;
        return reportUnsecuredDelegator.reportReferralSummaryViewModel(dto, pr, false);
    }

    @PostJsonReturningJson("/reports/referrals/summary/")
    public Stream<ReferralSummaryViewModel> reportReferralSummary(@RequestBody ReportCriteriaDto dto) {
        return this.reportReferralSummary(null, dto);
    }

    /**
     * Return the support work done according to the report criteria (only evidence group 19)
     */
    @PostJsonReturningJson("/reports/evidence/needs/page/{page}/")
    public Iterable<EvidenceSupportWorkViewModel> supportWorkByCriteria(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return allNeedsWorkByCriteria(page, dto, EvidenceGroup.NEEDS, true);
    }

    /**
     * Provide any work based on the support/needs table - currently this means supportStaffNotes and managerNotes, and no risk.
     * The criteria date range refers to the 'took place on' date.
     */
    private Iterable<EvidenceSupportWorkViewModel> allNeedsWorkByCriteria(int page, @NonNull ReportCriteriaDto dto,
                                                                          @NonNull EvidenceGroup evidenceGroup,
                                                                          boolean useAclSecurity) {

        JPQLQuery<EvidenceSupportWork> query = workWithReportCriteria(page, dto, evidenceGroup, useAclSecurity);

        // if we used projection, we could use what was here before with a queryModifier as in findGroupedWorkAnalysis
        // so it might be possible to put back to spring-data findAll using the same class as the projection - to reduce code
        List<EvidenceSupportWork> results = query.select(evidenceSupportWork)
                .fetch();

        return results.stream()
                .map(evidenceSupportWorkToEvidenceViewModel::apply).collect(toList());
    }

    private JPQLQuery<EvidenceSupportWork> workWithReportCriteria(int page, @NonNull ReportCriteriaDto dto,
                                                                  @NonNull EvidenceGroup evidenceGroup,
                                                                  boolean useAclSecurity) {
        Predicate p = reportUnsecuredDelegator.evidencePredicate(evidenceSupportWork._super._super, dto, Collections.singletonList(evidenceGroup), null, true, false, useAclSecurity);
        QPageRequest pr = supportPageRequest(page);

        JPQLQuery<EvidenceSupportWork> baseQuery = reportUnsecuredDelegator.query(evidenceSupportWork)
                .where(p);

//        JPQLQuery<evidenceSupportWork> query = applySupportWorkReferralCriteriaModifier(dto).apply(baseQuery);
        QueryModifier<EvidenceSupportWork> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto, referral, QReferralServiceRecipient.referralServiceRecipient, evidenceSupportWork.serviceRecipient);
        JPQLQuery<EvidenceSupportWork> query = queryModifier.apply(baseQuery);

        query = query.limit(pr.getPageSize()).offset(pr.getOffset());
        return query;
    }

    /**
     * Return the work done for a provided EvidenceGroup in the needs repository only - not threat.
     */
    @PostJsonReturningJson("/reports/evidence/needs/{evidenceGroup}/page/{page}/")
    public Iterable<EvidenceSupportWorkViewModel> needsWorkByCriteria(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        var grp = taskDefinitionService.findGroupFromGroupName(dto.getSupportEvidenceGroup());
        return allNeedsWorkByCriteria(page, dto, grp, true);
    }

    /**
     * Return the custom form work done according to the report criteria.
     * The criteria date range refers to the 'took place on' date.
     */
    @PostJsonReturningJson("/reports/evidence/form/{evidenceGroup}/page/{page}/")
    @ResponseStatus(HttpStatus.OK) // above defaults to CREATED
    public Iterable<EvidenceFormWorkViewModel> customFormWorkByCriteria(@PathVariable int page, @PathVariable String evidenceGroupKey, @RequestBody ReportCriteriaDto dto) {
        throw new UnsupportedOperationException("not yet implemented");
        /*
        long evidenceGroupId = EvidenceGroup.getAsEnum(evidenceGroupKey).id;

        Predicate p = reportUnsecuredDelegator.evidenceBasePredicate(qEFS, dto, qEFS.serviceRecipient.serviceAllocation); // work dates applied here
        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeCustomFormWork).asQPageRequest(page);

        QueryModifier<EvidenceFormSnapshot> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto, referral, QReferralServiceRecipient.referralServiceRecipient, evidenceSupportWork.serviceRecipient);

        final Iterable<EvidenceFormSnapshotViewModel> formSnapshotViewModels =
                evidenceFormSnapshotRepository.findAllServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(p, pr, queryModifier);
        return StreamSupport.stream(formSnapshotViewModels.spliterator(), false).map(questionnaireWorkSummaryToViewModel).collect(toList());
        */
    }

    /**
     * Return the risk work done according to the report criteria.
     * The criteria date range refers to the 'took place on' date.
     */
    @PostJsonReturningJson("/reports/evidence/threat/page/{page}/")
    public Iterable<EvidenceThreatWorkViewModel> riskWorkByCriteria(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        Predicate p = riskPredicate(dto);
        QPageRequest pr = riskPageRequest(page);

        JPQLQuery<EvidenceThreatWork> baseQuery = reportUnsecuredDelegator.query(evidenceThreatWork)
                .where(p);

//        JPQLQuery<evidenceThreatWork> query = applyRiskWorkReferralCriteriaModifier(dto).apply(baseQuery);
        QueryModifier<EvidenceThreatWork> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto, referral, QReferralServiceRecipient.referralServiceRecipient, evidenceThreatWork.serviceRecipient);
        JPQLQuery<EvidenceThreatWork> query = queryModifier.apply(baseQuery);

        query = query.limit(pr.getPageSize()).offset(pr.getOffset());

        // if we used projection, we could use what was here before with a queryModifier as in findGroupedWorkAnalysis
        // so it might be possible to put back to spring-data findAll using the same class as the projection - to reduce code
        List<EvidenceThreatWork> results = query.select(evidenceThreatWork).fetch();

        return results.stream()
                .map(evidenceThreatWorkToEvidenceViewModel::apply).collect(toList());
    }

    @PostJsonReturningJson("/reports/reviews/page/{page}/")
    public Iterable<ReviewViewModel> reportReviews(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        Predicate p = EntityRestrictionCommonPredicates.applySecurityPredicate(
                review.serviceRecipient, dto, entityRestrictionService, serviceCategorisationService);

        if ("liveAtEnd".equals(dto.getEntityStatus())) {
            // startDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(p, review.startDate, null, dto.getToDate());

            // get the latest startdate review per srId
            // startDate is the only option at the moment
            // this JPASubQuery is a clone of the below JPAQuery so that the sub query has the same criteria as the main query
            // so that we are looking for the latest/earliest within the criteria of serviceId, evidenceGroupId, workDate ranges etc
            QReview subQueryReviewAlias = new QReview("reviewAlias");
            // apply the same criteria as we use this subquery so the outer query can get the latest - so both need the criteria
            // we don't need the security predicate though if we are per srId
            Predicate subPDates = PredicateSupport.applyLocalDateRange(null, subQueryReviewAlias.startDate, null, dto.getToDate());
            JPQLQuery<Review> reviewSubQuery = JPAExpressions
                    // use an alias only - otherwise it generates a 'from table1,table2' cross join
                    .selectFrom(subQueryReviewAlias)
                    .where(
                            // use the report criteria to get the applicable dates (this also allows any future review dates, if any)
                            subPDates,
                            // the criteria of the sub query is to get the latest review for each serviceRecipient
                            subQueryReviewAlias.serviceRecipientId.eq(review.serviceRecipientId)
                    );

            BooleanExpression latestReview = review.startDate.goeAll(reviewSubQuery.select(subQueryReviewAlias.startDate));
            p = latestReview.and(p);
        } else {
            // startDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(p, review.startDate, dto.getFromDate(), dto.getToDate());
        }

        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeReviews).asQPageRequest(page);
//        QueryModifier<Review> queryModifier = applyBaseServiceRecipientToReferralCriteriaModifier(dto,
        QueryModifier<Review> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto,
                referral, QReferralServiceRecipient.referralServiceRecipient, review.serviceRecipient);

        // would be nice to use findAll(p, pr, queryModifier) but we'd be forced to use findAllWithProjection
        // so instead we are specifying a base query to apply ourselves
        JPQLQuery<Review> baseQuery = reportUnsecuredDelegator.query(review)
                .where(p);
        JPQLQuery<Review> query = queryModifier.apply(baseQuery);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        List<Review> reviews = query.select(review).fetch();

        return reviews.stream().map(reviewToViewModel::apply).collect(toList());
    }

    // NB ** COPY ** of reportFlagsRisk - until we can get EvidenceBaseFlag.work (which may be awkward) then
    // NB assumes all evidence groups
    @PostJsonReturningJson("/reports/support-flags/page/{page}/")
    public Iterable<EvidenceSupportFlagsViewModel> reportFlagsSupport(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        QReferralServiceRecipient refSr = QReferralServiceRecipient.referralServiceRecipient;
        QServiceCategorisation svcCat = QServiceCategorisation.serviceCategorisation;
        QEvidenceSupportFlag flag = QEvidenceSupportFlag.evidenceSupportFlag;
        QEvidenceSupportWork work = QEvidenceSupportWork.evidenceSupportWork;

        Predicate pSvcCat = EntityRestrictionCommonPredicates.applySecurityPredicate(
                svcCat, dto, entityRestrictionService, serviceCategorisationService);

        Predicate p;

        // snapshot-based (latest)
        // NB this is 'entityStatus' - different to 'referralStatus' - as we may want the latest risk flags on the latest (live) referrals
        if ("liveAtEnd".equals(dto.getEntityStatus())) {
            // workDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(null, work.workDate, null, dto.getToDate());

            // get the latest flags per srId
            // this JPASubQuery is a clone of the below JPAQuery so that the sub query has the same criteria as the main query
            // we don't need the security predicate duplicated though if we are per srId
            // so that we are looking for the latest/earliest within the criteria of serviceId, evidenceGroupId, workDate ranges etc
            QEvidenceSupportFlag subQueryFlagAlias = new QEvidenceSupportFlag("flagAlias");
            QEvidenceSupportWork subQueryWorkAlias = new QEvidenceSupportWork("workAlias");
            Predicate subPDates = PredicateSupport.applyLocalDateRange(null, subQueryWorkAlias.workDate, null, dto.getToDate());
            JPQLQuery<EvidenceSupportFlag> flagSubQuery = JPAExpressions
                    // use an alias only - otherwise it generates a 'from table1,table2' cross join
                    .selectFrom(subQueryFlagAlias)
                    .innerJoin(subQueryFlagAlias.work, subQueryWorkAlias)
                    .where(
                            // use the report criteria to get the applicable dates (this also allows any future review dates, if any)
                            subPDates,
                            // the criteria of the sub query is to get the latest flags for each serviceRecipient with the same dates
                            subQueryFlagAlias.serviceRecipient.id.eq(flag.serviceRecipient.id),
                            subQueryFlagAlias.flagDefId.eq(flag.flagDefId)
                    );

            BooleanExpression latestFlag = work.workDate.goeAll(flagSubQuery.select(subQueryWorkAlias.workDate));
            p = latestFlag.and(p);
        } else {
            // workDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(null, work.workDate, dto.getFromDate(), dto.getToDate());
        }

        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeRiskWorkFlags).asQPageRequest(page);

        // the predicate and queryModifier go hand-in-hand really since the queryModifier
        // applies an inner join on serviceRecipient (without it the query uses a cross join - which is all rows)
        QueryModifier<EvidenceSupportFlag> queryModifierSecurity = jpqlQuery -> jpqlQuery
                .join(flag.serviceRecipient.as(QReferralServiceRecipient.class), refSr)
                .join(refSr.serviceAllocation, svcCat)
                .where(pSvcCat);

        QueryModifier<EvidenceSupportFlag> queryModifierReferral = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto,
                referral, refSr, flag.serviceRecipient);

        QueryModifier<EvidenceSupportFlag> queryModifier = jpqlQuery -> queryModifierReferral.apply(queryModifierSecurity.apply(jpqlQuery));

        // would be nice to use findAll(p, pr, queryModifier) but we'd be forced to use findAllWithProjection
        // so instead we are specifying a base query to apply ourselves
        JPAQuery<EvidenceSupportFlag> baseQuery = reportUnsecuredDelegator.query(flag)
                .innerJoin(flag.work, work)
                .where(p);
        JPQLQuery<EvidenceSupportFlag> query = queryModifier.apply(baseQuery);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        List<EvidenceSupportFlag> flags = query.select(flag).fetch();

        // flags have work (EvidenceSupportFlag.work) which can be smart steps or questionnaires - there is no discriminator
        // it will have 'answers' eagerly loaded, so we now convert them in the view model
        return flags.stream().map(evidenceSupportFlagsToViewModel).collect(toList());
    }

    // NB ** COPY ** of reportFlagsSupport - until we can get EvidenceBaseFlag.work (which may be awkward) then
    // NB assumes all evidence groups
    @PostJsonReturningJson("/reports/risk-flags/page/{page}/")
    public Iterable<EvidenceThreatFlagsViewModel> reportFlagsRisk(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        QReferralServiceRecipient refSr = QReferralServiceRecipient.referralServiceRecipient;
        QServiceCategorisation svcCat = QServiceCategorisation.serviceCategorisation;
        QEvidenceThreatFlag flag = QEvidenceThreatFlag.evidenceThreatFlag;
        QEvidenceThreatWork work = evidenceThreatWork;

        Predicate pSvcCat = EntityRestrictionCommonPredicates.applySecurityPredicate(
                svcCat, dto, entityRestrictionService, serviceCategorisationService);

        Predicate p;

        // snapshot-based (latest)
        // NB this is 'entityStatus' - different to 'referralStatus' - as we may want the latest risk flags on the latest (live) referrals
        if ("liveAtEnd".equals(dto.getEntityStatus())) {
            // workDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(null, work.workDate, null, dto.getToDate());

            // get the latest flags per srId
            // this JPASubQuery is a clone of the below JPAQuery so that the sub query has the same criteria as the main query
            // we don't need the security predicate duplicated though if we are per srId
            // so that we are looking for the latest/earliest within the criteria of serviceId, evidenceGroupId, workDate ranges etc
            QEvidenceThreatFlag subQueryFlagAlias = new QEvidenceThreatFlag("flagAlias");
            QEvidenceThreatWork subQueryWorkAlias = new QEvidenceThreatWork("workAlias");
            Predicate subPDates = PredicateSupport.applyLocalDateRange(null, subQueryWorkAlias.workDate, null, dto.getToDate());
            JPQLQuery<EvidenceThreatFlag> flagSubQuery = JPAExpressions
                    // use an alias only - otherwise it generates a 'from table1,table2' cross join
                    .selectFrom(subQueryFlagAlias)
                    .innerJoin(subQueryFlagAlias.work, subQueryWorkAlias)
                    .where(
                            // use the report criteria to get the applicable dates (this also allows any future review dates, if any)
                            subPDates,
                            // the criteria of the sub query is to get the latest flags for each serviceRecipient with the same dates
                            subQueryFlagAlias.serviceRecipient.id.eq(flag.serviceRecipient.id),
                            subQueryFlagAlias.flagDefId.eq(flag.flagDefId)
                    );

            BooleanExpression latestFlag = work.workDate.goeAll(flagSubQuery.select(subQueryWorkAlias.workDate));
            p = latestFlag.and(p);
        } else {
            // workDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(null, work.workDate, dto.getFromDate(), dto.getToDate());
        }

        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeRiskWorkFlags).asQPageRequest(page);

        // the predicate and queryModifier go hand-in-hand really since the queryModifier
        // applies an inner join on serviceRecipient (without it the query uses a cross join - which is all rows)
        QueryModifier<EvidenceThreatFlag> queryModifierSecurity = jpqlQuery -> jpqlQuery
                .join(flag.serviceRecipient.as(QReferralServiceRecipient.class), refSr)
                .join(refSr.serviceAllocation, svcCat)
                .where(pSvcCat);

        QueryModifier<EvidenceThreatFlag> queryModifierReferral = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto,
                referral, refSr, flag.serviceRecipient);

        QueryModifier<EvidenceThreatFlag> queryModifier = jpqlQuery -> queryModifierReferral.apply(queryModifierSecurity.apply(jpqlQuery));

        // would be nice to use findAll(p, pr, queryModifier) but we'd be forced to use findAllWithProjection
        // so instead we are specifying a base query to apply ourselves
        JPAQuery<EvidenceThreatFlag> baseQuery = reportUnsecuredDelegator.query(flag)
                .innerJoin(flag.work, work)
                .where(p);
        JPQLQuery<EvidenceThreatFlag> query = queryModifier.apply(baseQuery);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        List<EvidenceThreatFlag> flags = query.select(flag).fetch();

        return flags.stream().map(evidenceThreatFlagsToViewModel).collect(toList());
    }

    // NB assumes all evidence groups
    @PostJsonReturningJson("/reports/risk-ratings/page/{page}/")
    public Iterable<EvidenceRiskAreaViewModel> reportRatings(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        QReferralServiceRecipient refSr = QReferralServiceRecipient.referralServiceRecipient;
        QServiceCategorisation svcCat = QServiceCategorisation.serviceCategorisation;
        QEvidenceThreatOutcome rating = QEvidenceThreatOutcome.evidenceThreatOutcome;
        QEvidenceThreatWork work = evidenceThreatWork;

        Predicate pSvcCat = EntityRestrictionCommonPredicates.applySecurityPredicate(
                svcCat, dto, entityRestrictionService, serviceCategorisationService);

        Predicate p;

        // snapshot-based (latest)
        // NB this is 'entityStatus' - different to 'referralStatus' - as we may want the latest risk flags on the latest (live) referrals
        if ("liveAtEnd".equals(dto.getEntityStatus())) {
            // workDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(null, work.workDate, null, dto.getToDate());

            // get the latest ratings per srId
            // this JPASubQuery is a clone of the below JPAQuery so that the sub query has the same criteria as the main query
            // we don't need the security predicate duplicated though if we are per srId
            // so that we are looking for the latest/earliest within the criteria of serviceId, evidenceGroupId, workDate ranges etc
            QEvidenceThreatOutcome subQueryRatingsAlias = new QEvidenceThreatOutcome("ratingsAlias");
            QEvidenceThreatWork subQueryWorkAlias = new QEvidenceThreatWork("workAlias");
            Predicate subPDates = PredicateSupport.applyLocalDateRange(null, subQueryWorkAlias.workDate, null, dto.getToDate());
            JPQLQuery<EvidenceThreatOutcome> ratingsSubQuery = JPAExpressions
                    // use an alias only - otherwise it generates a 'from table1,table2' cross join
                    .selectFrom(subQueryRatingsAlias)
                    .innerJoin(subQueryRatingsAlias.work, subQueryWorkAlias)
                    .where(
                            // use the report criteria to get the applicable dates (this also allows any future review dates, if any)
                            subPDates,
                            // the criteria of the sub query is to get the latest flags for each serviceRecipient with the same dates
                            subQueryRatingsAlias.serviceRecipient.id.eq(rating.serviceRecipient.id),
                            subQueryRatingsAlias.outcome.id.eq(rating.outcome.id)
                    );

            BooleanExpression latestRating = work.workDate.goeAll(ratingsSubQuery.select(subQueryWorkAlias.workDate));
            p = latestRating.and(p);
        } else {
            // workDate is the only option at the moment
            p = PredicateSupport.applyLocalDateRange(null, work.workDate, dto.getFromDate(), dto.getToDate());
        }

        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeRiskWorkFlags).asQPageRequest(page);

        // the predicate and queryModifier go hand-in-hand really since the queryModifier
        // applies an inner join on serviceRecipient (without it the query uses a cross join - which is all rows)
        QueryModifier<EvidenceThreatOutcome> queryModifierSecurity = jpqlQuery -> jpqlQuery
                .join(rating.serviceRecipient.as(QReferralServiceRecipient.class), refSr)
                .join(refSr.serviceAllocation, svcCat)
                .where(pSvcCat);

        QueryModifier<EvidenceThreatOutcome> queryModifierReferral = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto,
                referral, refSr, rating.serviceRecipient);

        QueryModifier<EvidenceThreatOutcome> queryModifier = jpqlQuery -> queryModifierReferral.apply(queryModifierSecurity.apply(jpqlQuery));

        // would be nice to use findAll(p, pr, queryModifier) but we'd be forced to use findAllWithProjection
        // so instead we are specifying a base query to apply ourselves
        JPAQuery<EvidenceThreatOutcome> baseQuery = reportUnsecuredDelegator.query(rating)
                .innerJoin(rating.work, work)
                .where(p);
        JPQLQuery<EvidenceThreatOutcome> query = queryModifier.apply(baseQuery);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        List<EvidenceThreatOutcome> ratings = query.select(rating).fetch();

        return ratings.stream().map(genericTypeRiskAreaToViewModel).collect(toList());
    }

    // NB TEST ONLY
    @PostJsonReturningJson("/reports/evidence/needs/work/snapshot/latestInRange/")
    public Iterable<EvidenceSupportWorkViewModel> latestSupportWorkByCriteria(@RequestBody ReportCriteriaDto dto) {
        return getSupportWorkSnapshotViewModels(null, dto, true, true);
    }

    /**
     * Return the latest support work done according to the report criteria (only evidence group 19)
     */
    @PostJsonReturningJson("/reports/evidence/needs/work/snapshot/latestInRange/page/{page}/")
    public Iterable<EvidenceSupportWorkViewModel> latestSupportWorkByCriteria(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getSupportWorkSnapshotViewModels(page, dto, true, true);
    }

    private Iterable<EvidenceSupportWorkViewModel> getSupportWorkSnapshotViewModels(Integer page,
                                                                                    ReportCriteriaDto dto,
                                                                                    boolean latest,
                                                                                    boolean useAclSecurity) {
        // TODO we probably could do better caching on reports - eg this from QuestionnaireEvidenceController
        //if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { return null; }

        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(dto.getSupportEvidenceGroup());
        List<EvidenceGroup> evidenceGroupArr = evidenceGroup != null ? Collections.singletonList(evidenceGroup) : null;

        QEvidenceSupportWork subQueryWorkAlias = new QEvidenceSupportWork("subWorkAlias");
        JPQLQuery<EvidenceSupportWork> query = this.reportUnsecuredDelegator.workSnapshotQuery(ReportController.qGTSW,
                QEvidenceSupportWork.evidenceSupportWork,
                subQueryWorkAlias, subQueryWorkAlias._super._super, dto, evidenceGroupArr, latest, useAclSecurity);

        if (page != null) {
            QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeSupportWork).asQPageRequest(page);
            query.limit(pr.getPageSize()).offset(pr.getOffset());
        }

        EvidenceSupportWorkToViewModel mapWork = new EvidenceSupportWorkToViewModel();
        return query.fetch().stream()
                .map(mapWork)
                .collect(toList());
    }

    @PostJsonReturningJson("/reports/evidence/threat/work/snapshot/latestInRange/page/{page}/")
    public Iterable<EvidenceThreatWorkViewModel> latestThreatWorkByCriteria(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getThreatWorkSnapshotViewModels(page, dto, true, true);
    }

    // NB a CLONE of getSupportWorkSnapshotViewModels - but only this method is a clone
    private Iterable<EvidenceThreatWorkViewModel> getThreatWorkSnapshotViewModels(Integer page,
                                                                                  ReportCriteriaDto dto,
                                                                                  boolean latest,
                                                                                  boolean useAclSecurity) {
        // TODO we probably could do better caching on reports - eg this from QuestionnaireEvidenceController
        //if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { return null; }

        List<EvidenceGroup> evidenceGroupArr = Collections.singletonList(EvidenceGroup.THREAT);

        QEvidenceThreatWork subQueryWorkAlias = new QEvidenceThreatWork("subWorkAlias");
        JPQLQuery<EvidenceThreatWork> query = this.reportUnsecuredDelegator.workSnapshotQuery(ReportController.qGTTW,
                QEvidenceThreatWork.evidenceThreatWork,
                subQueryWorkAlias, subQueryWorkAlias._super._super, dto, evidenceGroupArr, latest, useAclSecurity);

        if (page != null) {
            QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeSupportWork).asQPageRequest(page);
            query.limit(pr.getPageSize()).offset(pr.getOffset());
        }

        EvidenceThreatWorkToViewModel mapWork = new EvidenceThreatWorkToViewModel(false);
        return query.fetch().stream()
                .map(mapWork)
                .collect(toList());
    }

    @PostJsonReturningJson("/reports/evidence/form/work/snapshot/latest/page/{page}/")
    public Iterable<EvidenceFormWorkViewModel> latestCustomFormWorkByCriteria(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getFormSnapshotViewModels(page, dto, true, true);
    }

    private Iterable<EvidenceFormWorkViewModel> getFormSnapshotViewModels(Integer page,
                                                                          ReportCriteriaDto dto,
                                                                          boolean latest,
                                                                          boolean useAclSecurity) {
        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(dto.getCustomFormEvidenceGroup());
        List<EvidenceGroup> evidenceGroupArr = evidenceGroup != null ? Collections.singletonList(evidenceGroup) : null;

        QEvidenceFormWork subQueryWorkAlias = new QEvidenceFormWork("subWorkAlias");
        JPQLQuery<EvidenceFormWork> query = this.reportUnsecuredDelegator.workSnapshotQuery(ReportController.qEFS,
                QEvidenceFormWork.evidenceFormWork,
                subQueryWorkAlias, subQueryWorkAlias._super, dto, evidenceGroupArr, latest, useAclSecurity);

        if (page != null) {
            QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeCustomFormWork).asQPageRequest(page);
            query.limit(pr.getPageSize()).offset(pr.getOffset());
        }

        EvidenceFormWorkToViewModel mapWork = new EvidenceFormWorkToViewModel();
        return query.fetch().stream()
                .map(mapWork)
                .collect(toList());
    }

    /**
     * Return the questionnaire work done according to the report criteria.
     * The criteria date range refers to the 'took place on' date.
     */
    // TODO the evidenceGroupKey is now part of the ReportCriteriaDto - we should use that (see questionnaireAnswerSnapshotByCriteria below)
    @PostJsonReturningJson("/reports/evidence/questionnaire/{evidenceGroupKey}/page/{page}/")
    public Iterable<QuestionnaireEvidenceViewModel> questionnaireWorkByCriteria(
            @PathVariable int page, @PathVariable String evidenceGroupKey, @RequestBody ReportCriteriaDto dto) {
        var evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupKey);

        Predicate p = reportUnsecuredDelegator.evidencePredicate(qGTSW, dto, Collections.singletonList(evidenceGroup), null, true, false, true); // work dates applied here
        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeQuestionnaireWork).asQPageRequest(page);

        QueryModifier<QuestionnaireWorkSummary> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto, referral, QReferralServiceRecipient.referralServiceRecipient, evidenceSupportWork.serviceRecipient);

        final Iterable<QuestionnaireWorkSummary> questionnaireWork =
                questionnaireWorkRepository.findAllServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(p, pr, queryModifier);
        return StreamSupport.stream(questionnaireWork.spliterator(), false).map(questionnaireWorkSummaryToViewModel).collect(toList());
    }

    /**
     * Return all questionnaire work done according to the report criteria.
     * The criteria date range refers to the 'took place on' date.
     * All EVIDENCE_QUESTIONNAIRE's are found.
     */
    @PostJsonReturningJson("/reports/evidence/questionnaire/page/{page}/")
    public Iterable<QuestionnaireEvidenceViewModel> allQuestionnaireWorkByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        Predicate p = reportUnsecuredDelegator.evidencePredicate(qGTSW, dto, null, TaskDefinition.Type.EVIDENCE_QUESTIONNAIRE, false, true, true); // work dates applied here
        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeQuestionnaireWork).asQPageRequest(page);

        QueryModifier<QuestionnaireWorkSummary> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto, referral, QReferralServiceRecipient.referralServiceRecipient, evidenceSupportWork.serviceRecipient);

        final Iterable<QuestionnaireWorkSummary> questionnaireWork =
                questionnaireWorkRepository.findAllServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(p, pr, queryModifier);
        return StreamSupport.stream(questionnaireWork.spliterator(), false).map(questionnaireWorkSummaryToViewModel).collect(toList());
    }

    /**
     * Snapshot report which gets the current answers at the date of the report. NB This differs from getting answers in a date range.
     * We use the approach which is already available in QuestionAnswerSnapshotViewModel (which can be seen with QuestionnaireEvidenceController).
     * This is because we'd like to report on a whole questionnaire - eg a wellbeing questionnaire - which can come from many work items - so
     * QuestionnaireWorkSummary is not appropriate.
     * The current QuestionnaireEvidenceController is limited to one service recipient, so here its adapted using query-dsl.
     * Also see supportSmartStepsSnapshotLatestInRangeByCriteria et al.
     */
    @PostJsonReturningJson("/reports/evidence/questionnaire/snapshot/latestInRange/page/{page}/")
    public Iterable<QuestionnaireAnswersSnapshotViewModel> questionnaireAnswerSnapshotLatestInRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getQuestionnaireAnswersSnapshotViewModels(page, dto, true, true);
    }
    @PostJsonReturningJson("/reports/evidence/questionnaire/snapshot/latestBeforeRange/page/{page}/")
    public Iterable<QuestionnaireAnswersSnapshotViewModel> questionnaireAnswerSnapshotLatestBeforeRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        dto.setTo(dto.getFromDate().minusDays(1).toString());
        dto.setFrom(null);
        return getQuestionnaireAnswersSnapshotViewModels(page, dto, true, true);
    }
    @PostJsonReturningJson("/reports/evidence/questionnaire/snapshot/earliestInRange/page/{page}/")
    public Iterable<QuestionnaireAnswersSnapshotViewModel> questionnaireAnswerSnapshotEarliestInRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getQuestionnaireAnswersSnapshotViewModels(page, dto, false, true);
    }

    private Iterable<QuestionnaireAnswersSnapshotViewModel> getQuestionnaireAnswersSnapshotViewModels(int page,
                                                                                                      ReportCriteriaDto dto,
                                                                                                      boolean latest,
                                                                                                      boolean useAclSecurity) {
        // TODO we probably could do better caching on reports - eg this from QuestionnaireEvidenceController
        //  if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { return null; }

        List<EvidenceGroup> evidenceGroups = new ArrayList<>();
        EvidenceGroup evidenceGroupSingle = null;
        if (dto.getQuestionnaireEvidenceGroupArr() != null) {
            dto.getQuestionnaireEvidenceGroupArr().forEach(eg -> {
                evidenceGroups.add(taskDefinitionService.findGroupFromGroupName(eg));
            });
        } else {
            evidenceGroupSingle = taskDefinitionService.findGroupFromGroupName(dto.getQuestionnaireEvidenceGroup());
            evidenceGroups.add(evidenceGroupSingle);
        }

        JPQLQuery<EvidenceSupportAnswer> query = this.reportUnsecuredDelegator.questionnaireSnapshotQuery(dto, evidenceGroups, latest, useAclSecurity);

        // NB the page size for answers could do with being larger than that of questionnaire work
        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeQuestionnaireWork).asQPageRequest(page);
        query.limit(pr.getPageSize()).offset(pr.getOffset());

        return reportUnsecuredDelegator.getQuestionnaireAnswersSnapshotViewModels(QEvidenceSupportAnswer.evidenceSupportAnswer, evidenceGroupSingle, query);
    }

    /**
     * Snapshot report which gets the current actions at the date of the report. NB This differs from getting actions in a date range.
     * Also see questionnaireAnswerSnapshotLatestInRangeByCriteria et al.
     */
    @PostJsonReturningJson("/reports/evidence/needs/snapshot/latestInRange/page/{page}/")
    public Iterable<SupportSmartStepsSnapshotViewModel> supportSmartStepsSnapshotLatestInRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getSmartStepsSnapshotViewModels(page, dto, true, true);
    }
    @PostJsonReturningJson("/reports/evidence/needs/snapshot/latestBeforeRange/page/{page}/")
    public Iterable<SupportSmartStepsSnapshotViewModel> supportSmartStepsSnapshotLatestBeforeRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        dto.setTo(dto.getFromDate().minusDays(1).toString());
        dto.setFrom(null);
        return getSmartStepsSnapshotViewModels(page, dto, true, true);
    }
    @PostJsonReturningJson("/reports/evidence/needs/snapshot/earliestInRange/page/{page}/")
    public Iterable<SupportSmartStepsSnapshotViewModel> supportSmartStepsSnapshotEarliestInRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getSmartStepsSnapshotViewModels(page, dto, false, true);
    }

    @PostJsonReturningJson("/reports/evidence/threat/snapshot/latestInRange/page/{page}/")
    public Iterable<ThreatSmartStepsSnapshotViewModel> threatSmartStepsSnapshotLatestInRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getThreatSmartStepsSnapshotViewModels(page, dto, true, true);
    }
    @PostJsonReturningJson("/reports/evidence/threat/snapshot/latestBeforeRange/page/{page}/")
    public Iterable<ThreatSmartStepsSnapshotViewModel> threatSmartStepsSnapshotLatestBeforeRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        dto.setTo(dto.getFromDate().minusDays(1).toString());
        dto.setFrom(null);
        return getThreatSmartStepsSnapshotViewModels(page, dto, true, true);
    }
    @PostJsonReturningJson("/reports/evidence/threat/snapshot/earliestInRange/page/{page}/")
    public Iterable<ThreatSmartStepsSnapshotViewModel> threatSmartStepsSnapshotEarliestInRangeByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return getThreatSmartStepsSnapshotViewModels(page, dto, false, true);
    }

    private Iterable<SupportSmartStepsSnapshotViewModel> getSmartStepsSnapshotViewModels(int page, ReportCriteriaDto dto,
                                                                                  boolean latest, boolean useAclSecurity) {
        // TODO we probably could do better caching on reports - eg this from QuestionnaireEvidenceController
        //if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { return null; }

        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(dto.getSupportEvidenceGroup());
        List<EvidenceGroup> evidenceGroupArr = evidenceGroup != null ? Collections.singletonList(evidenceGroup) : null;

        var query = this.reportUnsecuredDelegator.supportActionInstanceSnapshotQuery(dto, evidenceGroupArr, latest, useAclSecurity);

        // NB the page size for answers could do with being larger than that of questionnaire work
        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeSupportWork).asQPageRequest(page);
        query.limit(pr.getPageSize()).offset(pr.getOffset());

        return reportUnsecuredDelegator.getSupportActionInstanceSnapshotViewModels(QEvidenceSupportAction.evidenceSupportAction, evidenceGroup, query);
    }

    // as per getSmartStepsSnapshotViewModels
    private Iterable<ThreatSmartStepsSnapshotViewModel> getThreatSmartStepsSnapshotViewModels(int page, ReportCriteriaDto dto,
                                                                                              boolean latest, boolean useAclSecurity) {
        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(dto.getSupportEvidenceGroup());
        List<EvidenceGroup> evidenceGroupArr = evidenceGroup != null ? Collections.singletonList(evidenceGroup) : null;

        var query = this.reportUnsecuredDelegator.threatActionInstanceSnapshotQuery(dto, evidenceGroupArr, latest, useAclSecurity);

        // NB the page size for answers could do with being larger than that of questionnaire work
        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeRiskWork).asQPageRequest(page);
        query.limit(pr.getPageSize()).offset(pr.getOffset());

        return reportUnsecuredDelegator.getThreatActionInstanceSnapshotViewModels(QEvidenceThreatAction.evidenceThreatAction, evidenceGroup, query);
    }

    /**
     * Return the task statuses according to the report criteria.
     * The criteria date range refers to the 'completed' date, since we'd expect
     * a report on due tasks to get everything.
     */
    @PostJsonReturningJson("/reports/tasks/page/{page}/")
    public Iterable<TaskStatusViewModel> taskStatusByCriteria(
            @PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        Predicate p = EntityRestrictionCommonPredicates.applySecurityPredicate(
                taskStatus.serviceRecipient, dto, entityRestrictionService, serviceCategorisationService);

        if (dto.getServiceRecipientFilter() != null) {
            p = taskStatus.serviceRecipientId.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":"))).and(p);
        }
        if (dto.getUserId() != null) {
            p = taskStatus.assignedUser.id.eq(dto.getUserId()).and(p);
        }

        boolean notCompleted = StringUtils.containsIgnoreCase(dto.getEntityStatus(), "incomplete");
        boolean hasDueDate = StringUtils.containsIgnoreCase(dto.getEntityStatus(), "hasDueDate") ||
                StringUtils.containsIgnoreCase(dto.getEntityStatus(), "dueDate");
        //boolean completed = !notCompleted && StringUtils.containsIgnoreCase(dto.getEntityStatus(), "completed");
        //boolean overdue = StringUtils.containsIgnoreCase(dto.getEntityStatus(), "overdue");

        // on tasks by month report loads 'dueByUsername', 'completedWithDueByUsername', 'overdueByUsername' and then sorts out
        // for us to get the same data (but with the actual task data), means we need to load with 'dueDateOrCompleted'
        // because it needs an 'or', and we need to retain any previous usage of this report - this will also include any overdue
        // since overdue is simply the overdue bits of those due (currently at least)
        boolean dueDateOrCompleted = StringUtils.equalsIgnoreCase(dto.getEntityStatus(), "dueDateOrCompleted");
        if (dueDateOrCompleted) {
            // for 'completedWithDueByUsername'
            var pCompleted = PredicateSupport.applyLocalDateRangeOnInstant(p, taskStatus.completed, dto.getFromDate(), dto.getToDate());
            pCompleted = and(pCompleted, taskStatus.dueDate.isNotNull());

            // for 'dueByUsername' and 'overdueByUsername'
            var pDue = PredicateSupport.applyLocalDateJdkRange(p, taskStatus.dueDate, null, dto.getToDate());
            var pDueNotCompleted = and(pDue, taskStatus.completed.isNull());

            p = or(pCompleted, pDueNotCompleted);
        } else {
            if (notCompleted) {
                p = and(p, taskStatus.completed.isNull());
            }
            if (hasDueDate) {
                p = and(p, taskStatus.dueDate.isNotNull());
            }
        }

        QPageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeTasks).asQPageRequest(page);
//        QueryModifier<TaskStatus> queryModifier = applyBaseServiceRecipientToReferralCriteriaModifier(dto,
        QueryModifier<TaskStatus> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto,
                referral, QReferralServiceRecipient.referralServiceRecipient, taskStatus.serviceRecipient);

        // would be nice to use findAll(p, pr, queryModifier) but we'd be forced to use findAllWithProjection
        // so instead we are specifying a base query to apply ourselves
        JPQLQuery<TaskStatus> baseQuery = new JPAQuery<>(em)
                .from(taskStatus)
                .select(taskStatus)
                .where(p)
                .orderBy(taskStatus.dueDate.desc());
        JPQLQuery<TaskStatus> query = queryModifier.apply(baseQuery);
        query.limit(pr.getPageSize()).offset(pr.getOffset());
        List<TaskStatus> taskStatuses = query.fetch();

        return taskStatuses.stream().map(taskStatusToViewModel).collect(toList());
    }

    // NB we probably don't need a report - we have one around attendances
    @PostJsonReturningJson("/reports/activities/page/{page}/")
    public ResourceList<GroupSupportActivitySummaryRowResource> reportGroupActivitySummaries(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeActivities).asPageRequest(page);
        return reportUnsecuredDelegator.reportGroupSupportActivitySummaries(dto, pr);
    }

    @PostJsonReturningJson("/reports/activities/attendance/page/{page}/")
    public Iterable<ClientAttendanceViewModel> reportGroupActivityByStartDate(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        JPQLQuery<GroupActivity_Referral> q = activityAttendanceQuery(dto);
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeAttendances).asPageRequest(page);
        q.limit(pr.getPageSize()).offset(pr.getOffset());

        List<GroupActivity_Referral> results = q.select(QGroupActivity_Referral.groupActivity_Referral).fetch();

        // NB this view model by default avoids loading associations (which would otherwise be loaded per entry)
        ClientAttendanceToViewModel toViewModel = new ClientAttendanceToViewModel(supportCommentRepository).withParentActivity();

        return results.stream().map(toViewModel).collect(toList());
    }

    @PostJsonReturningJson("/reports/agencies/page/{page}/")
    @ResponseStatus(HttpStatus.OK)
    public Iterable<AgencyViewModel> reportAgencies(@PathVariable int page, @RequestBody(required = false) ReportCriteriaDto dto) {
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeDefault).asPageRequest(page);
        final Iterable<Agency> agencies = agencyRepository.findAll(pr);

        return StreamSupport.stream(agencies.spliterator(), false)
                .map(agencyToViewModel).collect(toList());
    }

    @PostJsonReturningJson("/reports/professionals/page/{page}/")
    @ResponseStatus(HttpStatus.OK)
    public Iterable<IndividualViewModel> reportProfessionals(@PathVariable int page, @RequestBody(required = false) ReportCriteriaDto dto) {
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeDefault).asPageRequest(page);
        final Iterable<Individual> professionals = individualRepository.findByCompanyNotNull(pr);

        return StreamSupport.stream(professionals.spliterator(), false)
                .map(individualToViewModel).collect(toList());
    }

    /**
     * Return the referrals according to the report criteria.
     * The criteria date range refers to fields on the referral depending on the report criteria status.
     */
    @PostJsonReturningJson("/reports/addresshistory/page/{page}/")
    public Iterable<AddressHistoryViewModel> reportAddressHistory(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        var results = reportUnsecuredDelegator.addressHistory(page, dto, false);

        var convert = new AddressHistoryToViewModel();
        return results.stream().map(convert).collect(toList());
    }

    /**
     * Return the occupancy history according to the report criteria.
     * The criteria date range refers to fields on the occupancy depending on the report criteria status.
     * Gaps in the validFrom/validTo periods are filled with 'unoccupied' entries.
     */
    @PostJsonReturningJson("/reports/occupancy/page/{page}/")
    public Iterable<OccupancyViewModel> reportOccupancy(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {
        return reportUnsecuredDelegator.occupancy(page, dto);
    }

    @PostJsonReturningJson("/reports/contacts/page/{page}/")
    public Iterable<ServiceRecipientAssociatedContactViewModel> reportAssociatedContacts(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        JPQLQuery<ServiceRecipientContact> q = associatedContactQuery(dto);

        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeContacts).asPageRequest(page);
        q.limit(pr.getPageSize()).offset(pr.getOffset());

        List<ServiceRecipientContact> results = q.select(QServiceRecipientContact.serviceRecipientContact).fetch();

        return results.stream().map(associatedContactToViewModel).collect(toList());
    }

    // TODO required for 'activity' tab, but would be better existing in separate api entry point (also see findAllServiceRecipientArchivedCommandsByCreated)
    @PreAuthorize("hasAnyRole('ROLE_STAFF', 'ROLE_REPORTS')")
    @PostJsonReturningJson("/reports/service-recipients/commands/page/{page}/")
    public String findAllServiceRecipientCommandsByCreated(@PathVariable int page, @RequestBody ReportCriteriaDto dto,
                                                           @RequestParam(required = false) String order) {

        JPQLQuery<ServiceRecipientCommand> q = serviceRecipientCommandQuery(dto);
        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeCommands).asPageRequest(page);
        q.limit(pr.getPageSize()).offset(pr.getOffset());
        QServiceRecipientCommand qCommand = QServiceRecipientCommand.serviceRecipientCommand;
        if ("desc".equals(order)) {
            q.orderBy(qCommand.created.desc());
        } else {
            q.orderBy(qCommand.created.asc());
        }

        Iterable<ServiceRecipientCommand> results = q.select(QServiceRecipientCommand.serviceRecipientCommand).fetch();

        return extractJsonBody.asJsonArray(StreamSupport.stream(results.spliterator(), false));
    }

    /**
     * Retrieve deleted commands, which are good for audits but don't form part of the audits since they exist in the archive table.
     * We do not, at this stage, need to retrieve other details from the archive table.
     * It is paged but due to lack of ref-integrity to the services_projects table we require in-memory filtering.
     * NB A separate serviceAllocationId column may be the way forward to also avoid other referral-specific joins in this class.
     */
    @PreAuthorize("hasAnyRole('ROLE_STAFF', 'ROLE_REPORTS')")
    @PostJsonReturningJson("/reports/service-recipients/commands/deleted/page/{page}")
    public String findAllServiceRecipientDeletedCommandsByCreated(@PathVariable int page, @RequestBody ReportCriteriaDto dto) {

        QServiceRecipientCommandArchive qCommand = QServiceRecipientCommandArchive.serviceRecipientCommandArchive;
        Predicate pCreatedAndpUser = this.baseServiceRecipientCommandQuery(dto, qCommand._super);
        // NB deleteOnly, and NOT delete request
        // This means that when something is deleted, the requests no longer show - unless we want to work around the problem of finding the serviceId/projectId
        // because it can change over time - so we either need to store it in the body of request-delete (as delete does) or add serviceAllocationId to commands, or load the archived commands to check
        // but this seems a lot of work for not a log of gain at this point.
        BooleanExpression pDeleteOnly = qCommand.commandName.equalsIgnoreCase(DeleteServiceRecipientCommand.DISCRIMINATOR);
        JPAQuery<ServiceRecipientCommandArchive> q = reportUnsecuredDelegator.query(qCommand)
                .where(pCreatedAndpUser, pDeleteOnly)
                .orderBy(qCommand.created.asc());

        PageRequest pr = reportUnsecuredDelegator.getPageSize(PageSizeCommands).asPageRequest(page);
        q.limit(pr.getPageSize()).offset(pr.getOffset());

        List<ServiceRecipientCommandArchive> results = q.select(qCommand).fetch();

        ServicesProjectsDto restrictions = entityRestrictionService.getRestrictedServicesProjectsDto(serviceCategorisationService);
        Long reportServiceId = dto.getServiceId();
        Long reportProjectId = dto.getProjectId();
        List<ServiceRecipientCommandArchive> filtered = results.stream().filter(a -> {
            var serviceIdProjectId = getServiceIdProjectId(a.getBody());
            boolean matchPermission = restrictions.canAccess(serviceIdProjectId.getLeft(), serviceIdProjectId.getRight());
            boolean matchReport = reportServiceId == null || (reportServiceId.equals(serviceIdProjectId.getLeft())) && (serviceIdProjectId.getRight() == null || reportProjectId.equals(serviceIdProjectId.getRight()));
            return matchPermission && matchReport;
        }).collect(toList());

        return baseExtractJsonBody.asJsonArray(filtered);
    }

    private org.apache.commons.lang3.tuple.Pair<Long, Long> getServiceIdProjectId(String body) {
        // $.jsonViewModel.serviceId fails since jsonViewModel is wrapped in a quotes
        var conf = com.jayway.jsonpath.Configuration.defaultConfiguration()
                .addOptions(Option.SUPPRESS_EXCEPTIONS);
        String vm = JsonPath.read(body, "$.jsonViewModel");
        Integer serviceAllocationId = JsonPath.read(vm, "$.serviceAllocationId");
        if (serviceAllocationId != null) {
            var svcCat = serviceCategorisationService.getServiceCategorisation(serviceAllocationId);
            return org.apache.commons.lang3.tuple.Pair.of(svcCat.getServiceId(), svcCat.getProjectId() != null ? svcCat.getProjectId() : null);
        } else {
            int serviceId = JsonPath.read(vm, "$.serviceId");
            Integer projectId = JsonPath.using(conf).parse(vm).read("$.projectId");
            return org.apache.commons.lang3.tuple.Pair.of((long) serviceId, projectId != null ? projectId.longValue() : null);
        }
    }

    /* we don't need to worry about the performance hit of using 'readTree' on a few bits of data
    private Tuple2<Integer, Integer> getServiceIdProjectId(String body) {
        // use an index approach - see GoalCommandExtractJsonBody for alternatives
        int idxSvc = body.indexOf("\"serviceId\":");
        String svcFragment = body.substring(idxSvc + 12); // get past "serviceId":
        String svcIdStr = StringUtils.substringBefore(svcFragment, ",");
        int svcId = Integer.parseInt(svcIdStr);

        int idxPrj = body.indexOf("\"projectId\":");
        Integer prjId = null;
        if (idxPrj > -1) {
            String prjFragment = body.substring(idxSvc + 12); // get past "projectId":
            String prjIdStr = StringUtils.substringBefore(prjFragment, ",");
            prjId = Integer.parseInt(prjIdStr);
        }
        return Tuples.of(svcId, prjId);
    }
    */


    private Predicate baseServiceRecipientCommandQuery(ReportCriteriaDto dto, QBaseServiceRecipientCommand qCommand) {

        // remoteCreationTime could be used, its an 'instant' since epoch saved with jadira using PersistentInstantAsTimestamp
        // but created DateTime is probably less confusing as an event feed
        BooleanExpression pCreatedFrom = null;
        if (dto.getFromDate() != null) {
            pCreatedFrom = qCommand.created.goe(dto.getFromDate().toDateTimeAtStartOfDay(DateTimeZone.UTC));
        }
        BooleanExpression pCreatedTo = null;
        if (dto.getToDate() != null) {
            pCreatedTo = qCommand.created.before(dto.getToDate().toDateTimeAtStartOfDay(DateTimeZone.UTC).plusDays(1));
        }

        BooleanExpression pUser = dto.getUserId() == null ? null :
                qCommand.userId.eq(dto.getUserId());

        Predicate p = ExpressionUtils.allOf(pCreatedFrom, pCreatedTo, pUser);
        if (dto.getServiceRecipientFilter() != null) {
            p = qCommand.serviceRecipientId.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":")))
                    .and(p);
        }

        if (dto.getEntityStatus() != null) {
            // perhaps best to specify the escape character - https://www.iodigital.com/en/history/foreach/exact-matching-of-a-string-containing-a-wild-card-in-querydsl
            p = qCommand.body.likeIgnoreCase("%" + dto.getEntityStatus() + "%")
                    .and(p);
        }

        if (dto.getCommandNameArr() != null) {
            List<String> inCommands = dto.getCommandNameArr().stream()
                    .filter(cmd -> !cmd.startsWith("!"))
                    .collect(Collectors.toList());
            List<String> notInCommands = dto.getCommandNameArr().stream()
                    .filter(cmd -> cmd.startsWith("!"))
                    .map(cmd -> cmd.substring(1))
                    .collect(Collectors.toList());

            if (!inCommands.isEmpty()) {
                p = qCommand.commandName.in(inCommands).and(p);
            }
            if (!notInCommands.isEmpty()) {
                p = qCommand.commandName.notIn(notInCommands).and(p);
            }
        }

        if (dto.getUserId() != null) {
            p = qCommand.userId.eq(dto.getUserId())
                    .and(p);
        }

        return p;
    }

    // NB taskStatus reports have used a queryModifier - see applyBaseServiceRecipientToReferralCriteriaModifier
    // but this probably assumes all tasks are referral tasks
    private JPQLQuery<ServiceRecipientCommand> serviceRecipientCommandQuery(ReportCriteriaDto dto) {

        QServiceCategorisation qSvcCat = QServiceCategorisation.serviceCategorisation;
        // NB remove the cross join, which we will want to mitigate using explicit inner join
        // NB we still get one cross join to services_projects, but we do with all now since serviceAllocation is one more level away from service/project
        Predicate pServiceRecipient = EntityRestrictionCommonPredicates.applySecurityPredicate(qSvcCat, dto, entityRestrictionService, serviceCategorisationService);

        QServiceRecipientCommand qCommand = QServiceRecipientCommand.serviceRecipientCommand;
        Predicate pCreatedAndpUser = this.baseServiceRecipientCommandQuery(dto, qCommand._super);

        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager
        QBaseServiceRecipient qServiceRecipient = QBaseServiceRecipient.baseServiceRecipient;
        JPAQuery<ServiceRecipientCommand> query = reportUnsecuredDelegator.query(qCommand)
                .innerJoin(qCommand.serviceRecipient, qServiceRecipient)
                .innerJoin(qServiceRecipient.serviceAllocation, qSvcCat)
                .where(pServiceRecipient, pCreatedAndpUser);

        return query;
    }

    // TODO wip - just enough for some data flow, doesn't even return the right data
    //  and doesn't obey acl or latest answer
    @PostJsonReturningJson("/reports/answersByQuestion/groupBy/")
    public Iterable<CountsByEntityViewModel> findSummarisedQuestionAnswers(@RequestBody ReportCriteriaDto dto) {

        Predicate p = null;// ReferralPredicates.referralPredicate(dto, entityRestrictionService);

        QQuestion subQuestionAlias = new QQuestion("qnAlias");
        QCountsByEntityViewModel projection = new QCountsByEntityViewModel(
                subQuestionAlias.id,
                subQuestionAlias.name,
                // NB this will give the number of answers per qn not the number per answer
                QEvidenceSupportAnswer.evidenceSupportAnswer.answer.count().intValue());

        return supportAnswerRepository.findAllWithProjection(
                projection, p, jpqlQuery -> jpqlQuery
                        .innerJoin(QEvidenceSupportAnswer.evidenceSupportAnswer.question, subQuestionAlias)
                        .groupBy(
                                subQuestionAlias.id,
                                subQuestionAlias.name
                        )
                        .orderBy(
                                subQuestionAlias.name.asc()
                        ));
    }


    @PostJsonReturningJson("/reports/referralsByService/groupBy/")
    public Iterable<CountsByEntityViewModel> findSummarisedReferralsGroupedByService(@RequestBody ReportCriteriaDto dto) {

        QCountsByEntityViewModel projection = new QCountsByEntityViewModel(
                referral.serviceRecipient.serviceAllocation.service.id,
                referral.serviceRecipient.serviceAllocation.service.name,
                referral.count().intValue());

        Predicate p = ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);

        return referralRepository.findAllWithProjection(
                projection, p, jpqlQuery -> jpqlQuery
                        .leftJoin(referral.serviceRecipient)
                        .leftJoin(referral.serviceRecipient.serviceAllocation.service)
                        .groupBy(
                                referral.serviceRecipient.serviceAllocation.service.id,
                                referral.serviceRecipient.serviceAllocation.service.name
                        )
                        .orderBy(
                                referral.serviceRecipient.serviceAllocation.service.id.asc()
                        ));
    }

    @PostJsonReturningJson("/reports/referralsBySource/groupBy/")
    public Iterable<CountsByEntityViewModel> findSummarisedReferralGroupedBySource(@RequestBody ReportCriteriaDto dto) {

        Predicate p = ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);

        QAgency subAgencyAlias = new QAgency("agencyAlias");
        QListDefinitionEntry subAgencyCatAlias = new QListDefinitionEntry("catAlias");
        QCountsByEntityViewModel projection = new QCountsByEntityViewModel(
                subAgencyCatAlias.id,
                subAgencyCatAlias.name,
                referral.count().intValue());

        return referralRepository.findAllWithProjection(
                projection, p, jpqlQuery -> jpqlQuery
                        .innerJoin(referral.agency, subAgencyAlias)
                        .innerJoin(subAgencyAlias.agencyCategory, subAgencyCatAlias)
                        //.where(.isNotNull().and(referral.agency.agencyCategory.isNotNull()))
                        .groupBy(
                                subAgencyCatAlias.id,
                                subAgencyCatAlias.name
                        )
                        .orderBy(
                                subAgencyCatAlias.name.asc()
                        ));
    }

    @PostJsonReturningJson("/reports/referralsByEthnicity/groupBy/")
    public Iterable<CountsByEntityViewModel> findSummarisedReferralGroupedByEthnicity(@RequestBody ReportCriteriaDto dto) {
        return findSummarisedReferralGroupedByListDef(dto, referral.client.ethnicOrigin);
    }

    @PostJsonReturningJson("/reports/referralsBySexualOrientation/groupBy/")
    public Iterable<CountsByEntityViewModel> findSummarisedReferralGroupedBySexualOrientation(@RequestBody ReportCriteriaDto dto) {
        return findSummarisedReferralGroupedByListDef(dto, referral.client.sexuality);
    }

    @PostJsonReturningJson("/reports/referralsByDisability/groupBy/")
    public Iterable<CountsByEntityViewModel> findSummarisedReferralGroupedByDisability(@RequestBody ReportCriteriaDto dto) {
        return findSummarisedReferralGroupedByListDef(dto, referral.client.disability);
    }

    private Iterable<CountsByEntityViewModel> findSummarisedReferralGroupedByListDef(@RequestBody ReportCriteriaDto dto, QListDefinitionEntry path) {

        Predicate p = ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);

        QListDefinitionEntry subListDefAlias = new QListDefinitionEntry("listDefAlias");
        QCountsByEntityViewModel projection = new QCountsByEntityViewModel(
                subListDefAlias.id,
                subListDefAlias.name,
                referral.count().intValue());

        return referralRepository.findAllWithProjection(
                projection, p, jpqlQuery -> jpqlQuery
                        .innerJoin(path, subListDefAlias)
                        .groupBy(
                                subListDefAlias.id,
                                subListDefAlias.name
                        )
                        .orderBy(
                                subListDefAlias.name.asc()
                        ));
    }

    @PostJsonReturningJson("/reports/referralsByMonth/groupBy/{dateField}/")
    public Iterable<CountsByMonthViewModel> findSummarisedReferralsGroupedByMonth(
            @PathVariable String dateField, @RequestBody ReportCriteriaDto dto) {

        DateTimePath<DateTime> datePath = dateField.equals("exitedDate")
                ? referral.exited
                : referral.receivedDate;

        QCountsByMonthViewModel projection = new QCountsByMonthViewModel(
                referral.serviceRecipient.serviceAllocation.service.id,
                referral.serviceRecipient.serviceAllocation.service.name,
                datePath.year(),
                datePath.month(),
                datePath.count().intValue());

        Predicate p = ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);

        return referralRepository.findAllWithProjection(
                projection, p, jpqlQuery -> jpqlQuery
                        .leftJoin(referral.serviceRecipient)
                        .leftJoin(referral.serviceRecipient.serviceAllocation.service)
                        .groupBy(
                                datePath.year(),
                                datePath.month(),
                                referral.serviceRecipient.serviceAllocation.service.id,
                                referral.serviceRecipient.serviceAllocation.service.name
                        )
                        .orderBy(
                                datePath.year().desc(),
                                datePath.month().desc(),
                                referral.serviceRecipient.serviceAllocation.service.id.asc()
                        ));
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @PostJsonReturningJson("/reports/tasksByMonth/groupBy/{groupBy}/")
    public Iterable<CountsByMonthViewModel> findSummarisedTasksGroupedByMonth(
            @PathVariable String groupBy, @RequestBody ReportCriteriaDto dto) {

        // ensure security of the tasks we see
        Predicate p = EntityRestrictionCommonPredicates.applySecurityPredicate(taskStatus.serviceRecipient, dto, entityRestrictionService, serviceCategorisationService);

        boolean groupByUsername = StringUtils.contains(groupBy, "ByUsername");
        // dueWithCompleted because we shouldn't look for completed without a due date
        boolean completed = StringUtils.containsIgnoreCase(groupBy, "completed"); // completedWithDueByService
        boolean overdue = StringUtils.containsIgnoreCase(groupBy, "overdue"); // overdueByService, overdueByUsername

        // get the tasks we want - due or completed ones
        // CLONE in taskStatusByCriteria
        DateTimePath datePath = taskStatus.dueDate;
        if (completed) {
            datePath = taskStatus.completed;
            p = PredicateSupport.applyLocalDateRangeOnInstant(p, datePath, dto.getFromDate(), dto.getToDate());
            // completed tasks isn't so useful against 'due' - it would need to be 'created'
            p = and(p, taskStatus.dueDate.isNotNull());
            // completed over target different data types
            //if (overdue) {
                //p = and(p, taskStatus.completed.goe(taskStatus.dueDate));
            //}
        } else {
            p = PredicateSupport.applyLocalDateJdkRange(p, datePath, dto.getFromDate(), dto.getToDate());
            // this means out of those due in the range, these are the overdue ones... which is sort of okay
            if (overdue) {
                p = and(p, taskStatus.dueDate.loe(LocalDateTime.now()));
            }
        }

        NumberPath<Long> entityId = taskStatus.serviceRecipient.serviceAllocation.service.id;
        StringExpression entityName = taskStatus.serviceRecipient.serviceAllocation.service.name;;
        if (groupByUsername) {
            entityId = taskStatus.assignedUser.id;
            entityName = getIndividualNameExpression(taskStatus.assignedUser.contact.as(QIndividual.class));
        }

        QCountsByMonthViewModel projection = new QCountsByMonthViewModel(
                entityId,
                entityName,
                taskStatus.taskDefinitionId,
                datePath.year(),
                datePath.month(),
                datePath.count().intValue());

        JPQLQuery<CountsByMonthViewModel> baseQuery = new JPAQuery<>(em)
                .from(taskStatus)
                .leftJoin(taskStatus.serviceRecipient)
                .leftJoin(taskStatus.serviceRecipient.serviceAllocation.service)
                .groupBy(
                        datePath.year(),
                        datePath.month(),
                        entityId,
                        entityName,
                        taskStatus.taskDefinitionId
                )
                .orderBy(
                        datePath.year().desc(),
                        datePath.month().desc(),
                        entityId.asc(),
                        taskStatus.taskDefinitionId.asc()
                )
                .select(projection)
                .where(p);

        // applies PredicateSupport.ReferralReportCriteria (not the whole ReferralPredicates.referralPredicate and so misses hideOnList etc)
        QueryModifier<CountsByMonthViewModel> queryModifier = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto,
                referral, QReferralServiceRecipient.referralServiceRecipient, taskStatus.serviceRecipient);

        JPQLQuery<CountsByMonthViewModel> query = queryModifier.apply(baseQuery);

        return query.fetch();
    }


    @PostJsonReturningJson("/reports/groupedWorkAnalysis/needs/groupBy/{groupBy}/")
    public Iterable<WorkAnalysisGroupSummaryViewModel> findGroupedWorkAnalysis(@PathVariable String groupBy, @RequestBody ReportCriteriaDto dto) {

        // currently groupBy can be 'serviceRecipient.service' or 'author' (despite getFieldExpression)
        // On Oracle, 'author' fails with 'Caused by: java.sql.SQLSyntaxErrorException: ORA-00979: not a GROUP BY expression'.
        // On MySQL this shows the projection to have a contactId - no doubt due to the complexity of
        // getKeyLabelExpression, which could be simplified into two variables, and re-joined on the client
        // but for now we just use a dummy placeholder to allow us to project the contactId
        QIndividual ind = evidenceSupportWork.author.as(QIndividual.class);
        QWorkAnalysisGroupSummaryViewModel projection = groupBy.equals("author")
                ? new QWorkAnalysisGroupSummaryViewModel(
                    evidenceSupportWork.author.id.intValue(), ind.firstName, ind.lastName, evidenceSupportWork.count().intValue(),
                    evidenceSupportWork.workDate.max(),
                    evidenceSupportWork.comment.minutesSpent.sum(),
                    evidenceSupportWork.comment.minutesSpent.avg(),
                    evidenceSupportWork.signature.count().intValue()
                )
                : new QWorkAnalysisGroupSummaryViewModel(
                    getKeyExpression(groupBy), getKeyLabelExpression(groupBy), evidenceSupportWork.count().intValue(),
                    evidenceSupportWork.workDate.max(),
                    evidenceSupportWork.comment.minutesSpent.sum(),
                    evidenceSupportWork.comment.minutesSpent.avg(),
                    evidenceSupportWork.signature.count().intValue()
                );

        QueryModifier<EvidenceSupportWork> queryModifier = groupBy.equals("author")
                ? jpqlQuery -> jpqlQuery
                    .leftJoin(evidenceSupportWork.signature)
                    .groupBy(evidenceSupportWork.author.id, ind.firstName, ind.lastName)
                : jpqlQuery -> jpqlQuery
                    .leftJoin(evidenceSupportWork.signature)
                    .groupBy(getFieldExpression(groupBy), getKeyLabelExpression(groupBy));

        Predicate p = reportUnsecuredDelegator.evidencePredicate(evidenceSupportWork._super._super, dto, Collections.singletonList(EvidenceGroup.NEEDS), null, true, false, true);

        QueryModifier<EvidenceSupportWork> queryModifierWithReferral = jpqlQuery -> {
            JPQLQuery<EvidenceSupportWork> modified = queryModifier.apply(jpqlQuery);
            QueryModifier<EvidenceSupportWork> modifyWithAlias = reportUnsecuredDelegator.applyBaseServiceRecipientToReferralCriteriaModifier(dto, referral, QReferralServiceRecipient.referralServiceRecipient, evidenceSupportWork.serviceRecipient);
            return modifyWithAlias.apply(modified);
        };

        return supportWorkRepository.findAllWithProjection(projection, p, queryModifierWithReferral);
    }

    /**
     * return a label to use when describing this grouping
     */
    private StringExpression getKeyLabelExpression(String groupBy) {
        switch (groupBy) {
            case "serviceRecipient.service":
                return evidenceSupportWork.serviceRecipient.serviceAllocation.service.name;
            case "author":
                QIndividual ind = evidenceSupportWork.author.as(QIndividual.class);
                return getIndividualNameExpression(ind);

            default:
                throw new IllegalArgumentException("Unable to create key expression for: " + groupBy);
        }
    }

    private StringExpression getIndividualNameExpression(QIndividual ind) {
        StringExpression first = new CaseBuilder()
                .when(ind.firstName.isNull())
                .then("")
                .otherwise(ind.firstName);
        return new CaseBuilder()
                .when(ind.lastName.isNull())
                .then(first)
                .otherwise(first.append(" ").append(ind.lastName));
    }

    private NumberPath<? extends Number> getFieldExpression(String groupBy) {
        switch (groupBy) {
            case "serviceRecipient.company":
                return evidenceSupportWork.serviceRecipient.serviceAllocation.companyId;
            case "serviceRecipient.clientGroup":
                return evidenceSupportWork.serviceRecipient.serviceAllocation.clientGroupId;
            case "serviceRecipient.service":
                return evidenceSupportWork.serviceRecipient.serviceAllocation.service.id;
            case "serviceRecipient.serviceGroup":
                return evidenceSupportWork.serviceRecipient.serviceAllocation.serviceGroupId;
            case "author":
                return evidenceSupportWork.author.id;

            default:
                throw new IllegalArgumentException("Unable to create key expression for: " + groupBy);
        }
    }

    private Expression<Integer> getKeyExpression(String groupBy) {
        return getFieldExpression(groupBy).intValue();
    }

    private Predicate riskPredicate(ReportCriteriaDto dto) {
        log.info("Running report for work criteria " + dto.toString());

        Predicate p = EntityRestrictionCommonPredicates.applySecurityPredicate(
                evidenceThreatWork.serviceRecipient, dto, entityRestrictionService, serviceCategorisationService);
        p = PredicateSupport.applyLocalDateRange(p, evidenceThreatWork.workDate, dto.getFromDate(), dto.getToDate());

        if (dto.getServiceRecipientFilter() != null) {
            p = evidenceThreatWork.serviceRecipientId.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":")))
                .and(p);
        }

        return p;
    }

    private <T> JPQLQuery<T> activityAttendanceQuery(ReportCriteriaDto dto) {
        log.info("Running report for attendance criteria " + dto.toString());

        /* CHOICES OF IMPL: chose 2c for flexibility (though possibly 2b would have been neater)

           1) use a spring-data method on the repo: eg findByMultiIdActivity_FromDateBetween(LocalDateTime from, LocalDateTime to);
             (see GroupSupportActivityController.findGroupActivityInvolvementByReferralId)
             BUT this List return type doesn't allow us to extend the query with referral limitations
                a) we could return the type we need

           2) use GroupActivity_Referral in querydsl - eg QGroupActivity_Referral.groupActivity_Referral.multiId.referral.serviceRecipient._super
             BUT serviceRecipient was null/not in the query since "By default Querydsl initializes only reference properties of the first two levels"
             QueryInit doesn't exist on GroupActivity_Referral_MultiId.referral (it does on other properties in GroupActivity_Referral_MultiId)
                a) we could use QueryInit on a referral - its only the query language and shouldn't relate to eager loading
                b) we could use QueryInit on a new ServiceRecipient property
                c) we could join referrals that we want to restrict by BUT this needs to return JPQLQuery, not just a Predicate

           3) use a projection with ClientAttendanceViewModel - see GroupSupportActivityController.findAllUsingProjection
             BUT via a projection we need to re-provide the impl in ClientAttendanceToViewModel
        */

        // common alias to use in joining activity and referral crtieria
        QGroupActivity_Referral referralOnGroupActivity = QGroupActivity_Referral.groupActivity_Referral;
        QGroupSupportActivity groupActivity = QGroupSupportActivity.groupSupportActivity;
        QReferralServiceRecipient serviceRecipient = QReferralServiceRecipient.referralServiceRecipient;

        // our security criteria
        Predicate pServiceRecipient = EntityRestrictionCommonPredicates.applySecurityPredicate(serviceRecipient._super._super, dto, entityRestrictionService, serviceCategorisationService);

        // ** TODO avoid showing the report criteria on the screen since its not really relevant (by default we end up with a clause for 'receivedDate' for the date range)
        // NB it also introduces a cross join, which we will want to mitigate using explicit inner join
        //pServiceRecipient = applyReportCriteria(pServiceRecipient, serviceRecipient._super, dto, dto.getFromDate(), dto.getToDate(), false);

        // our activity criteria
        Predicate pActivity = PredicateSupport.applyLocalDateRange(null,
                groupActivity.fromDate,
                dto.getFromDate(),
                dto.getToDate());

        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager

        return new JPAQuery<T>(em)
                .from(referralOnGroupActivity)
                .join(referralOnGroupActivity.multiId.activity, groupActivity)
                .join(referralOnGroupActivity.multiId.referral.serviceRecipient, serviceRecipient)
                .where(pServiceRecipient, pActivity);

    }

    // NB taskStatus reports have used a queryModifier - see applyBaseServiceRecipientToReferralCriteriaModifier
    // but this probably assumes all tasks are referral tasks
    private JPQLQuery<ServiceRecipientContact> associatedContactQuery(ReportCriteriaDto dto) {

        QServiceCategorisation qSvcCat = QServiceCategorisation.serviceCategorisation;
        // NB remove the cross join, which we will want to mitigate using explicit inner join
        // NB we still get one cross join to services_projects, but we do with all now since serviceAllocation is one more level away from service/project
        Predicate pServiceRecipient = EntityRestrictionCommonPredicates.applySecurityPredicate(qSvcCat, dto, entityRestrictionService, serviceCategorisationService);

        QServiceRecipientContact qContact = QServiceRecipientContact.serviceRecipientContact;

        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager
        QBaseServiceRecipient qServiceRecipient = QBaseServiceRecipient.baseServiceRecipient;
        return reportUnsecuredDelegator.query(qContact)
                .innerJoin(qContact.serviceRecipient, qServiceRecipient)
                .innerJoin(qServiceRecipient.serviceAllocation, qSvcCat)
                .where(pServiceRecipient);
    }

    private QPageRequest supportPageRequest(int page) {
        // apply sort order to match code getting individual referral work - see SupportEvidenceController and SupportWorkRepositoryImpl
        // where the ts may expect the order - see ReportDataSourceFactory.ts
        QSort s = new QSort(
                evidenceSupportWork.serviceRecipient.id.asc(), // asc is probably the natural ordering of the query
                evidenceSupportWork.workDate.desc(),
                evidenceSupportWork.created.desc());
        return reportUnsecuredDelegator.getPageSize(PageSizeSupportWork).asQPageRequest(page, s);
    }

    private QPageRequest riskPageRequest(int page) {
        QSort s = new QSort(
                evidenceThreatWork.serviceRecipient.id.asc(),
                evidenceThreatWork.workDate.desc(),
                evidenceThreatWork.created.desc());
        return reportUnsecuredDelegator.getPageSize(PageSizeRiskWork).asQPageRequest(page, s);
    }

}